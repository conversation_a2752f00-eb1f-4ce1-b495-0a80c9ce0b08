import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/lib/AuthContext";
import EmailVerificationRequired from "./EmailVerificationRequired";

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireVerification?: boolean;
  showVerificationUI?: boolean;
  featureName?: string;
  verificationMessage?: string;
}

/**
 * A wrapper component that redirects to the login page if the user is not authenticated
 * or to a verification page if email verification is required but not completed
 *
 * @param children - The content to render if the user is authenticated and verified
 * @param redirectTo - The path to redirect to if the user is not authenticated
 * @param requireVerification - Whether to require email verification
 * @param showVerificationUI - Whether to show the verification UI instead of redirecting
 * @param featureName - The name of the feature being protected (for the verification UI)
 * @param verificationMessage - Custom message for the verification UI
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = '/signin',
  requireVerification = true,
  showVerificationUI = false,
  featureName = "access this feature",
  verificationMessage
}) => {
  const { currentUser, loading, emailVerified } = useAuth();
  const location = useLocation();

  // If the authentication is still loading, show a loading spinner
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-burgundy-500"></div>
      </div>
    );
  }

  // If the user is not authenticated, redirect to the login page
  if (!currentUser) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // If email verification is required but the user's email is not verified
  if (requireVerification) {
    // Double-check the emailVerified status from the auth context
    const isVerified = emailVerified;
    console.log("ProtectedRoute: Email verification required, current status:", isVerified);

    // If not verified, enforce access restriction
    if (!isVerified) {
      console.log("ProtectedRoute: Email not verified, restricting access");

      // If showVerificationUI is true, show the verification UI instead of redirecting
      if (showVerificationUI) {
        return <EmailVerificationRequired featureName={featureName} message={verificationMessage} />;
      }

      // Otherwise, redirect to the verification page
      return <Navigate to="/verify-email" state={{ from: location }} replace />;
    } else {
      console.log("ProtectedRoute: Email verified, allowing access");
    }
  }

  // If the user is authenticated and email is verified (if required), render the children
  return <>{children}</>;
};

export default ProtectedRoute;
