
import { useState, useMemo } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { UserPlus, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button-variants";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useAuth } from "@/lib/AuthContext";
import { indianStates } from "@/data/indianStates";
import { indianCities } from "@/data/indianCities";
import { apartments } from "@/data/apartments";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
import { GeoCoordinates, getCurrentPosition } from "@/lib/geolocationUtils";
import { OAuthButtons } from "./OAuthButtons";

// Create a base schema for common fields
const baseJoinSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().min(10, { message: "Please enter a valid phone number" }).max(15),
  address: z.string().min(3, { message: "Please enter your address" }),
  // Apartment field is still in the schema but not displayed in the UI
  // It will be implemented later, so we make it optional with a default empty string
  apartment: z.string().optional().default(""),
  city: z.string().min(2, { message: "Please select your city" }),
  state: z.string().min(2, { message: "Please select your state" }),
  pincode: z.string().min(6, { message: "Please enter a valid pincode" }).max(6),
  // Make GPS coordinates required
  gpsCoordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }, { required_error: "Location is required. Please click 'Get My Current Location'" }),
});

// Schema for regular email/password registration
export const joinSchema = baseJoinSchema.extend({
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Schema for OAuth users (no password fields required)
export const oAuthJoinSchema = baseJoinSchema;

export type JoinValues = z.infer<typeof joinSchema>;
export type OAuthJoinValues = z.infer<typeof oAuthJoinSchema>;

export const JoinForm = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { signUp, currentUser, isNewUser, setIsNewUser, reloadUser } = useAuth();

  // Determine if we're in OAuth mode based on the current user and isNewUser flag
  const isOAuthMode = currentUser && isNewUser;

  // Use the appropriate schema based on the mode
  const formSchema = isOAuthMode ? oAuthJoinSchema : joinSchema;

  // Create form with the appropriate type
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: currentUser?.displayName || "",
      email: currentUser?.email || "",
      password: "",
      confirmPassword: "",
      phone: "",
      address: "",
      apartment: "",
      city: "",
      state: "",
      pincode: "",
      gpsCoordinates: null,
    },
  });

  // State for managing city dropdown based on selected state
  const [selectedState, setSelectedState] = useState<string>("");
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [cityOptions, setCityOptions] = useState<ComboboxOption[]>([]);

  // State for GPS location
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [hasLocation, setHasLocation] = useState(false);

  // Apartment options are preserved in the code but not displayed in the UI
  // This will be implemented later
  const apartmentOptions = useMemo<ComboboxOption[]>(() => {
    const options = apartments.map(apt => ({ value: apt, label: apt }));
    console.log("Created apartment options:", options.length);
    return options;
  }, []);

  // Convert states array to ComboboxOption format for searchable dropdown
  const stateOptions = useMemo<ComboboxOption[]>(() => {
    const options = indianStates.map(state => ({ value: state, label: state }));
    console.log("Created state options:", options.length);
    return options;
  }, []);

  const onSubmit = async (values: any) => {
    setIsLoading(true);
    try {
      console.log("Registration data:", values);
      console.log("GPS Coordinates:", values.gpsCoordinates);
      console.log("Is OAuth mode:", isOAuthMode);

      // Common required fields for both modes
      const requiredFields = ['name', 'email', 'phone', 'address', 'city', 'state', 'pincode', 'gpsCoordinates'];

      // Add password fields for regular registration
      if (!isOAuthMode) {
        requiredFields.push('password', 'confirmPassword');
      }

      // Validate all required fields
      const missingFields = requiredFields.filter(field => !values[field]);
      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      if (isOAuthMode && currentUser) {
        // For OAuth users, we need to update their user document with additional information
        // and send a verification email if not already verified
        console.log("Updating OAuth user profile with additional information");

        try {
          // Dynamically import Firestore functions
          const { doc, setDoc, getFirestore } = await import('firebase/firestore');
          const db = getFirestore();

          // Create or update the user document with the additional information
          await setDoc(doc(db, 'users', currentUser.uid), {
            uid: currentUser.uid,
            email: currentUser.email,
            displayName: values.name,
            phone: values.phone,
            address: values.address,
            apartment: values.apartment || "", // Preserve apartment field in the database
            city: values.city,
            state: values.state,
            pincode: values.pincode,
            gpsCoordinates: values.gpsCoordinates,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            emailVerified: currentUser.emailVerified,
            photoURL: currentUser.photoURL,
            providerId: currentUser.providerId || "oauth"
          }, { merge: true });

          console.log("OAuth user profile updated successfully");

          // Reset the isNewUser flag
          setIsNewUser(false);

          // For social login users, we always want to enforce email verification
          // regardless of whether the provider marks the email as verified

          // First, reload the user to get the latest verification status
          try {
            console.log("Reloading user to get latest verification status...");
            const isVerified = await reloadUser();
            console.log("User reloaded, verification status:", isVerified);

            if (isVerified) {
              // If the email is already verified, proceed to dashboard
              console.log("Email already verified, proceeding to dashboard");
              toast.success("Profile completed successfully! Your email is already verified.");
              navigate("/dashboard");
              return;
            }
          } catch (reloadError) {
            console.error("Error reloading user:", reloadError);
            // Continue with verification flow
          }

          // If we get here, the email is not verified or there was an error checking
          // Send verification email as a reminder
          try {
            // Dynamically import Firebase auth functions
            const { sendEmailVerification } = await import('firebase/auth');

            console.log("Sending verification email to OAuth user after profile completion...");
            await sendEmailVerification(currentUser);
            console.log("Verification email sent successfully");

            toast.success("Profile completed! Please verify your email to access all features.");
            toast.info("A verification email has been sent. Please check your inbox and spam folder.");

            // Always redirect to verification page
            navigate("/verify-email");
          } catch (verificationError) {
            console.error("Error sending verification email:", verificationError);
            const errorMessage = verificationError instanceof Error ? verificationError.message : "Failed to send verification email";
            toast.error(errorMessage);

            // Still redirect to verification page even if sending fails
            navigate("/verify-email");
          }
        } catch (firestoreError) {
          console.error("Error updating OAuth user profile:", firestoreError);
          throw new Error("Failed to update profile. Please try again.");
        }
      } else {
        // For regular email/password registration
        console.log("Calling signUp function for email/password registration...");
        const user = await signUp(
          values.email,
          values.password,
          values.name,
          {
            phone: values.phone,
            address: values.address,
            apartment: values.apartment || "", // Preserve apartment field in the database
            city: values.city,
            state: values.state,
            pincode: values.pincode,
            gpsCoordinates: values.gpsCoordinates
          }
        );

        console.log("User created successfully:", user);

        toast.success("Account created successfully! Please check your email for verification.");
        navigate("/verify-email");
      }
    } catch (error) {
      console.error("Registration error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create account";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  // Handle state selection and update cities dropdown
  const handleStateChange = (state: string) => {
    console.log("handleStateChange called with state:", state);
    setSelectedState(state);
    form.setValue("state", state);

    // Clear city when state changes
    form.setValue("city", "");

    // Update available cities based on selected state
    if (state && indianCities[state]) {
      const cities = indianCities[state];
      setAvailableCities(cities);

      // Convert cities to ComboboxOption format
      const options = cities.map(city => ({ value: city, label: city }));
      console.log("Created city options:", options.length);
      setCityOptions(options);
    } else {
      setAvailableCities([]);
      setCityOptions([]);
    }
  };

  // Get current GPS location with improved error handling and user feedback
  const handleGetLocation = async () => {
    setIsGettingLocation(true);
    setLocationError(null);

    // Show initial toast to indicate the process has started
    const loadingToast = toast.loading("Getting your location...");

    try {
      // First attempt with high accuracy
      console.log("Attempting to get location...");

      try {
        // Try with high accuracy first
        const coordinates = await getCurrentPosition({
          enableHighAccuracy: true,
          timeout: 15000
        });

        console.log("Got high-accuracy coordinates:", coordinates);
        form.setValue("gpsCoordinates", coordinates);
        setHasLocation(true);

        // Dismiss the loading toast and show success
        toast.dismiss(loadingToast);
        toast.success("Location captured successfully!");

      } catch (error: any) {
        console.warn("High accuracy location failed:", error);

        // If it's a timeout error, try with lower accuracy
        if (error?.code === 'TIMEOUT') {
          toast.dismiss(loadingToast);
          toast.loading("Location request taking longer than expected. Trying with lower accuracy...");

          const coordinates = await getCurrentPosition({
            enableHighAccuracy: false,
            timeout: 20000,
            maximumAge: 60000 // Accept a cached position up to 1 minute old
          });

          console.log("Got low-accuracy coordinates:", coordinates);
          form.setValue("gpsCoordinates", coordinates);
          setHasLocation(true);
          toast.dismiss();
          toast.success("Location captured with lower accuracy");
        } else {
          // For other errors, rethrow to be caught by the outer catch
          throw error;
        }
      }
    } catch (error: any) {
      console.error("Error getting location:", error);

      // Dismiss any pending toasts
      toast.dismiss(loadingToast);

      // Set appropriate error message based on error type
      let errorMessage = "Failed to get location. Please try again.";

      if (error?.code === 'PERMISSION_DENIED') {
        errorMessage = "Location access denied. Please enable location services in your browser settings.";
      } else if (error?.code === 'TIMEOUT') {
        errorMessage = "Location request timed out. Please check your internet connection and try again.";
      } else if (error?.code === 'POSITION_UNAVAILABLE') {
        errorMessage = "Location information is unavailable. Please try again in a different area.";
      }

      setLocationError(errorMessage);
      setHasLocation(false);
      toast.error(errorMessage);

      // Provide guidance on how to fix common issues
      if (error?.code === 'PERMISSION_DENIED') {
        setTimeout(() => {
          toast.info("To enable location: Click the lock/info icon in your browser's address bar and allow location access.");
        }, 1000);
      }
    } finally {
      setIsGettingLocation(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-md">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-navy-800 font-playfair mb-2">Join PeerBooks</h1>
          <p className="text-gray-600">Create your account to start exchanging books</p>
          <p className="mt-2 text-burgundy-600 font-medium">All fields marked with <span className="text-red-500">*</span> are mandatory</p>
          {isOAuthMode && (
            <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-blue-800 font-medium">
                You're signed in with {currentUser?.providerId || "social login"}. Please complete your profile to continue.
              </p>
              <p className="text-blue-700 text-sm mt-2">
                <strong>Important:</strong> A verification email has been sent to your email address. After completing this form, you'll need to verify your email address before accessing your account.
              </p>
              <p className="text-blue-700 text-sm mt-2">
                <strong>Note:</strong> If you don't see the verification email, check your spam folder or we can resend it after you complete this form.
              </p>
            </div>
          )}
        </div>

        {!isOAuthMode && (
          <>
            <OAuthButtons actionType="Sign up" isDisabled={isLoading} />

            <div className="relative flex items-center justify-center my-6">
              <div className="border-t border-gray-300 w-full"></div>
              <div className="bg-white px-3 text-gray-500 text-sm">OR</div>
              <div className="border-t border-gray-300 w-full"></div>
            </div>
          </>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John Doe"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      type="email"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="+91 9876543210"
                      type="tel"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    For verification and contact purposes
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {!isOAuthMode && (
              <>
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input
                          placeholder="••••••••"
                          type="password"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        At least 6 characters
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input
                          placeholder="••••••••"
                          type="password"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your street address"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


            <FormField
              control={form.control}
              name="state"
              render={({ field }) => {
                console.log("Rendering state field with value:", field.value);
                return (
                  <FormItem>
                    <FormLabel>State <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Combobox
                        options={stateOptions}
                        value={field.value || ""}
                        onChange={(value) => {
                          console.log("State selected:", value);
                          handleStateChange(value);
                        }}
                        placeholder="Search or select state"
                        disabled={isLoading}
                        emptyMessage="No states found"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="city"
              render={({ field }) => {
                console.log("Rendering city field with value:", field.value);
                return (
                  <FormItem>
                    <FormLabel>City <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Combobox
                        options={cityOptions}
                        value={field.value || ""}
                        onChange={(value) => {
                          console.log("City selected:", value);
                          field.onChange(value);
                        }}
                        placeholder={selectedState ? "Search or select city" : "Select state first"}
                        disabled={isLoading || !selectedState}
                        emptyMessage={selectedState ? "No cities found" : "Please select a state first"}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="pincode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pincode <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter 6-digit pincode"
                      disabled={isLoading}
                      maxLength={6}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gpsCoordinates"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>GPS Location <span className="text-red-500">*</span></FormLabel>
                  <div className="space-y-2">
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-md text-sm mb-2">
                      <p className="text-blue-800">
                        <strong>Why we need your location:</strong> This helps match you with nearby book owners for easier exchanges.
                      </p>
                    </div>

                    <Button
                      type="button"
                      variant={field.value ? "outline" : "default"}
                      onClick={handleGetLocation}
                      disabled={isLoading || isGettingLocation}
                      className={`w-full flex items-center justify-center gap-2 ${!field.value ? 'bg-burgundy-600 hover:bg-burgundy-700 text-white font-medium py-3' : ''}`}
                    >
                      <MapPin className="h-4 w-4" />
                      {isGettingLocation ? (
                        <>
                          <span className="animate-pulse">Getting Location...</span>
                          <span className="loading-dots"></span>
                        </>
                      ) : field.value ? (
                        "Update My Location"
                      ) : (
                        "Get My Current Location (Required)"
                      )}
                    </Button>

                    {field.value && (
                      <div className="p-3 bg-green-50 border border-green-200 rounded-md text-sm">
                        <p className="font-medium text-green-800">Location captured successfully!</p>
                        <p className="text-green-700 mt-1">
                          Latitude: {field.value.latitude.toFixed(6)},
                          Longitude: {field.value.longitude.toFixed(6)}
                        </p>
                      </div>
                    )}

                    {locationError && !field.value && (
                      <div className="p-3 bg-red-50 border border-red-200 rounded-md text-sm">
                        <p className="text-red-700">{locationError}</p>
                        <p className="text-red-700 mt-2 font-medium">Troubleshooting tips:</p>
                        <ul className="list-disc pl-5 text-red-700 text-xs mt-1">
                          <li>Make sure location services are enabled in your device settings</li>
                          <li>Allow location access when prompted by your browser</li>
                          <li>Try refreshing the page and trying again</li>
                          <li>If on mobile, make sure GPS is enabled</li>
                        </ul>
                      </div>
                    )}

                    <FormDescription>
                      {!field.value && !locationError && "Your location will be used to find book owners near you"}
                    </FormDescription>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Apartment field removed from UI but preserved in the codebase for future implementation */}

            <Button
              type="submit"
              className="w-full flex items-center justify-center gap-2"
              disabled={isLoading || !hasLocation}
            >
              <UserPlus className="h-4 w-4" />
              {isLoading
                ? isOAuthMode ? "Saving Profile..." : "Creating Account..."
                : !hasLocation
                  ? "Get Location First"
                  : isOAuthMode
                    ? "Complete Profile & Verify Email"
                    : "Create Account & Verify Email"
              }
            </Button>
            {!hasLocation && (
              <p className="text-amber-600 text-sm text-center mt-2">
                Please get your current location before creating an account
              </p>
            )}
          </form>
        </Form>

        <div className="text-center mt-6">
          <p className="text-gray-600">
            Already have an account? {" "}
            <Link to="/signin" className="text-burgundy-500 hover:underline font-medium">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};
