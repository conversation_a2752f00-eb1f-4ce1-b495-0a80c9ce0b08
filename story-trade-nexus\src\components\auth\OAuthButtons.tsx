
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button-variants";
import { toast } from "sonner";
import { useNavigate, Link } from "react-router-dom";
import { useAuth } from "@/lib/AuthContext";

type OAuthProviderProps = {
  actionType: "Sign in" | "Sign up";
  isDisabled?: boolean;
};

export const OAuthButtons = ({
  actionType,
  isDisabled = false,
}: OAuthProviderProps) => {
  const navigate = useNavigate();
  const { signInWithGoogle, signInWithFacebook, isNewUser, setIsNewUser } = useAuth();
  const [loading, setLoading] = useState<string | null>(null);

  const handleGoogleAuth = async () => {
    setLoading("Google");
    try {
      console.log(`${actionType} with Google`);

      const user = await signInWithGoogle();

      if (isNewUser) {
        // If this is a new user, redirect to the registration form
        // Email verification is sent immediately during signInWithGoogle
        toast.success("Google authentication successful! Please complete your profile to continue.");
        toast.info("A verification email has been sent to your email address. You'll need to verify your email before accessing all features.");
        navigate("/join");
      } else {
        // For existing users, always check if email is verified
        // We need to reload the user to get the latest verification status
        try {
          const { reload: firebaseReloadUser } = await import('firebase/auth');
          await firebaseReloadUser(user);
          console.log("Existing Google user reloaded in OAuthButtons");
        } catch (reloadError) {
          console.error("Error reloading existing Google user in OAuthButtons:", reloadError);
          // Continue with the current user state
        }

        // After reload, check verification status again
        if (!user.emailVerified) {
          console.log("Existing Google user email not verified, redirecting to verification page");
          toast.warning("Please verify your email before accessing all features.");
          navigate("/verify-email");
          return;
        }

        console.log("Existing Google user email verified, proceeding to dashboard");
        toast.success("Signed in with Google successfully!");
        navigate("/dashboard");
      }
    } catch (error) {
      console.error(`Google authentication error:`, error);
      const errorMessage = error instanceof Error ? error.message : "Failed to authenticate with Google";
      toast.error(errorMessage);
    } finally {
      setLoading(null);
    }
  };

  const handleFacebookAuth = async () => {
    setLoading("Facebook");
    try {
      console.log(`${actionType} with Facebook`);

      const user = await signInWithFacebook();

      if (isNewUser) {
        // If this is a new user, redirect to the registration form
        // Email verification is sent immediately during signInWithFacebook
        toast.success("Facebook authentication successful! Please complete your profile to continue.");
        toast.info("A verification email has been sent to your email address. You'll need to verify your email before accessing all features.");
        navigate("/join");
      } else {
        // For existing users, always check if email is verified
        // We need to reload the user to get the latest verification status
        try {
          const { reload: firebaseReloadUser } = await import('firebase/auth');
          await firebaseReloadUser(user);
          console.log("Existing Facebook user reloaded in OAuthButtons");
        } catch (reloadError) {
          console.error("Error reloading existing Facebook user in OAuthButtons:", reloadError);
          // Continue with the current user state
        }

        // After reload, check verification status again
        if (!user.emailVerified) {
          console.log("Existing Facebook user email not verified, redirecting to verification page");
          toast.warning("Please verify your email before accessing all features.");
          navigate("/verify-email");
          return;
        }

        console.log("Existing Facebook user email verified, proceeding to dashboard");
        toast.success("Signed in with Facebook successfully!");
        navigate("/dashboard");
      }
    } catch (error) {
      console.error(`Facebook authentication error:`, error);
      const errorMessage = error instanceof Error ? error.message : "Failed to authenticate with Facebook";
      toast.error(errorMessage);
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="space-y-4 mb-6">
      <Button
        onClick={handleGoogleAuth}
        variant="outline"
        className="w-full flex items-center justify-center gap-2"
        disabled={isDisabled || loading !== null}
      >
        <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-google">
          <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"></path>
          <path d="M17.8 12.2H12.5V14.8H15.9C15.6 16 14.2 17.5 12 17.5C9.5 17.5 7.5 15.5 7.5 13C7.5 10.5 9.5 8.5 12 8.5C13.2 8.5 14.2 9 14.9 9.8L16.9 7.8C15.5 6.5 13.9 5.8 12 5.8C8 5.8 4.8 9 4.8 13C4.8 17 8 20.2 12 20.2C15.7 20.2 18.8 17.8 18.8 13.3C18.8 12.8 18.8 12.5 18.7 12.2H17.8Z"></path>
        </svg>
        {loading === "Google" ? "Processing..." : `${actionType} with Google`}
      </Button>

      <Button
        onClick={handleFacebookAuth}
        variant="outline"
        className="w-full flex items-center justify-center gap-2"
        disabled={isDisabled || loading !== null}
      >
        <svg viewBox="0 0 24 24" width="16" height="16" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-facebook">
          <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
        </svg>
        {loading === "Facebook" ? "Processing..." : `${actionType} with Facebook`}
      </Button>

      <div className="mt-4 text-xs text-center text-gray-500">
        By continuing, you agree to our{" "}
        <Link to="/terms" className="text-burgundy-500 hover:underline">
          Terms of Service
        </Link>{" "}
        and{" "}
        <Link to="/privacy" className="text-burgundy-500 hover:underline">
          Privacy Policy
        </Link>
        .
      </div>
    </div>
  );
};
