[![Build Status](https://github.com/firebase/firebase-admin-node/workflows/Continuous%20Integration/badge.svg)](https://github.com/firebase/firebase-admin-node/actions)

# Firebase Admin Node.js SDK


## Table of Contents

 * [Overview](#overview)
 * [Installation](#installation)
 * [Contributing](#contributing)
 * [Documentation](#documentation)
 * [Supported Environments](#supported-environments)
 * [Acknowledgments](#acknowledgments)
 * [License](#license)


## Overview

[Firebase](https://firebase.google.com) provides the tools and infrastructure
you need to develop your app, grow your user base, and earn money. The Firebase
Admin Node.js SDK enables access to Firebase services from privileged environments
(such as servers or cloud) in Node.js.

For more information, visit the
[Firebase Admin SDK setup guide](https://firebase.google.com/docs/admin/setup/).


## Installation

The Firebase Admin Node.js SDK is available on npm as `firebase-admin`:

```bash
$ npm install --save firebase-admin
```

To use the module in your application, `require` it from any JavaScript file:

```js
const { initializeApp } = require("firebase-admin/app");

initializeApp();
```

If you are using ES2015, you can `import` the module instead:

```js
import { initializeApp } from "firebase-admin/app";

initializeApp();
```


## Contributing

Please refer to the [CONTRIBUTING page](./CONTRIBUTING.md) for more information
about how you can contribute to this project. We welcome bug reports, feature
requests, code review feedback, and also pull requests.


## Supported Environments

We support Node.js 18 and higher.

Please also note that the Admin SDK should only
be used in server-side/back-end environments controlled by the app developer.
This includes most server and serverless platforms (both on-premise and in
the cloud). It is not recommended to use the Admin SDK in client-side
environments.


## Documentation

* [Setup Guide](https://firebase.google.com/docs/admin/setup/)
* [Database Guide](https://firebase.google.com/docs/database/admin/start/)
* [Authentication Guide](https://firebase.google.com/docs/auth/admin/)
* [Cloud Messaging Guide](https://firebase.google.com/docs/cloud-messaging/admin/)
* [API Reference](https://firebase.google.com/docs/reference/admin/node/)
* [Release Notes](https://firebase.google.com/support/release-notes/admin/node/)


## Acknowledgments

Thanks to the team at [Casetext](https://casetext.com/) for transferring
ownership of the `firebase-admin` npm module over to the Firebase team
and for their longtime use and support of the Firebase platform.


## License

Firebase Admin Node.js SDK is licensed under the
[Apache License, version 2.0](http://www.apache.org/licenses/LICENSE-2.0).

Your use of Firebase is governed by the
[Terms of Service for Firebase Services](https://firebase.google.com/terms/).
