
import { useState, useMemo } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { UserPlus, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button-variants";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useAuth } from "@/lib/AuthContext";
import { indianStates } from "@/data/indianStates";
import { indianCities } from "@/data/indianCities";
import { apartments } from "@/data/apartments";
import { Combobox, ComboboxOption } from "@/components/ui/combobox";
// GPS location capture has been removed

// Create the schema for registration
const joinSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  phone: z.string().min(10, { message: "Please enter a valid phone number" }).max(15),
  address: z.string().min(3, { message: "Please enter your address" }),
  // Apartment field is still in the schema but not displayed in the UI
  // It will be implemented later, so we make it optional with a default empty string
  apartment: z.string().optional().default(""),
  city: z.string().min(2, { message: "Please select your city" }),
  state: z.string().min(2, { message: "Please select your state" }),
  pincode: z.string().min(6, { message: "Please enter a valid pincode" }).max(6),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export type JoinValues = z.infer<typeof joinSchema>;

export const JoinForm = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { signUp } = useAuth();

  // Create form with the appropriate type
  const form = useForm<JoinValues>({
    resolver: zodResolver(joinSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      phone: "",
      address: "",
      apartment: "",
      city: "",
      state: "",
      pincode: "",
    },
  });

  // State for managing city dropdown based on selected state
  const [selectedState, setSelectedState] = useState<string>("");
  const [availableCities, setAvailableCities] = useState<string[]>([]);
  const [cityOptions, setCityOptions] = useState<ComboboxOption[]>([]);

  // Apartment options are preserved in the code but not displayed in the UI
  // This will be implemented later
  const apartmentOptions = useMemo<ComboboxOption[]>(() => {
    const options = apartments.map(apt => ({ value: apt, label: apt }));
    console.log("Created apartment options:", options.length);
    return options;
  }, []);

  // Convert states array to ComboboxOption format for searchable dropdown
  const stateOptions = useMemo<ComboboxOption[]>(() => {
    const options = indianStates.map(state => ({ value: state, label: state }));
    console.log("Created state options:", options.length);
    return options;
  }, []);

  const onSubmit = async (values: JoinValues) => {
    setIsLoading(true);
    try {
      console.log("Registration data:", values);

      // For email/password registration
      console.log("Calling signUp function for email/password registration...");
      const user = await signUp(
        values.email,
        values.password,
        values.name,
        {
          phone: values.phone,
          address: values.address,
          apartment: values.apartment || "", // Preserve apartment field in the database
          city: values.city,
          state: values.state,
          pincode: values.pincode
        }
      );

      console.log("User created successfully:", user);

      toast.success("Account created successfully! Please check your email for verification.");
      navigate("/verify-email");
    } catch (error) {
      console.error("Registration error:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to create account";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  // Handle state selection and update cities dropdown
  const handleStateChange = (state: string) => {
    console.log("handleStateChange called with state:", state);
    setSelectedState(state);
    form.setValue("state", state);

    // Clear city when state changes
    form.setValue("city", "");

    // Update available cities based on selected state
    if (state && indianCities[state]) {
      const cities = indianCities[state];
      setAvailableCities(cities);

      // Convert cities to ComboboxOption format
      const options = cities.map(city => ({ value: city, label: city }));
      console.log("Created city options:", options.length);
      setCityOptions(options);
    } else {
      setAvailableCities([]);
      setCityOptions([]);
    }
  };

  // GPS location capture functionality has been removed

  return (
    <div className="container mx-auto px-4 py-8 max-w-md">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold text-navy-800 font-playfair mb-2">Join PeerBooks</h1>
          <p className="text-gray-600">Create your account to start exchanging books</p>
          <p className="mt-2 text-burgundy-600 font-medium">All fields marked with <span className="text-red-500">*</span> are mandatory</p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John Doe"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      type="email"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Phone Number <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="+91 9876543210"
                      type="tel"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    For verification and contact purposes
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {!isOAuthMode && (
              <>
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input
                          placeholder="••••••••"
                          type="password"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        At least 6 characters
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm Password <span className="text-red-500">*</span></FormLabel>
                      <FormControl>
                        <Input
                          placeholder="••••••••"
                          type="password"
                          disabled={isLoading}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter your street address"
                      disabled={isLoading}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />


            <FormField
              control={form.control}
              name="state"
              render={({ field }) => {
                console.log("Rendering state field with value:", field.value);
                return (
                  <FormItem>
                    <FormLabel>State <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Combobox
                        options={stateOptions}
                        value={field.value || ""}
                        onChange={(value) => {
                          console.log("State selected:", value);
                          handleStateChange(value);
                        }}
                        placeholder="Search or select state"
                        disabled={isLoading}
                        emptyMessage="No states found"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="city"
              render={({ field }) => {
                console.log("Rendering city field with value:", field.value);
                return (
                  <FormItem>
                    <FormLabel>City <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Combobox
                        options={cityOptions}
                        value={field.value || ""}
                        onChange={(value) => {
                          console.log("City selected:", value);
                          field.onChange(value);
                        }}
                        placeholder={selectedState ? "Search or select city" : "Select state first"}
                        disabled={isLoading || !selectedState}
                        emptyMessage={selectedState ? "No cities found" : "Please select a state first"}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />

            <FormField
              control={form.control}
              name="pincode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pincode <span className="text-red-500">*</span></FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter 6-digit pincode"
                      disabled={isLoading}
                      maxLength={6}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* GPS location field has been removed */}
            {/* Apartment field removed from UI but preserved in the codebase for future implementation */}

            <Button
              type="submit"
              className="w-full flex items-center justify-center gap-2"
              disabled={isLoading}
            >
              <UserPlus className="h-4 w-4" />
              {isLoading ? "Creating Account..." : "Create Account & Verify Email"}
            </Button>
          </form>
        </Form>

        <div className="text-center mt-6">
          <p className="text-gray-600">
            Already have an account? {" "}
            <Link to="/signin" className="text-burgundy-500 hover:underline font-medium">
              Sign In
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};
