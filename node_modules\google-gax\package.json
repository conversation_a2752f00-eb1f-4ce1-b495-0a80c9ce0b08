{"name": "google-gax", "version": "4.6.1", "description": "Google API Extensions", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src", "build/protos/", "!build/src/**/*.map"], "dependencies": {"@grpc/grpc-js": "^1.10.9", "@grpc/proto-loader": "^0.7.13", "@types/long": "^4.0.0", "abort-controller": "^3.0.0", "duplexify": "^4.0.0", "google-auth-library": "^9.3.0", "node-fetch": "^2.7.0", "object-hash": "^3.0.0", "proto3-json-serializer": "^2.0.2", "retry-request": "^7.0.0", "uuid": "^9.0.1", "protobufjs": "^7.3.2"}, "devDependencies": {"@types/uuid": "^9.0.7", "@babel/plugin-proposal-private-methods": "^7.18.6", "@types/mocha": "^9.0.0", "@types/ncp": "^2.0.1", "@types/node": "^20.5.0", "@types/node-fetch": "^2.6.11", "@types/object-hash": "^3.0.0", "@types/proxyquire": "^1.3.28", "@types/pumpify": "^1.4.1", "@types/sinon": "^17.0.0", "@types/uglify-js": "^3.17.0", "c8": "^9.0.0", "cheerio": "1.0.0-rc.12", "codecov": "^3.1.0", "execa": "^5.0.0", "glob": "10.4.5", "google-proto-files": "^4.2.0", "gts": "^5.0.0", "jackspeak": "3.4.3", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^4.0.0", "lru-cache": "10.4.3", "long": "^4.0.0", "mkdirp": "^2.0.0", "mocha": "^9.0.0", "ncp": "^2.0.0", "null-loader": "^4.0.0", "pdfmake": "0.2.12", "protobufjs-cli": "1.1.3", "proxyquire": "^2.0.1", "pumpify": "^2.0.0", "sinon": "^18.0.0", "stream-events": "^1.0.4", "ts-loader": "^8.0.0", "typescript": "^5.1.6", "uglify-js": "^3.17.0", "walkdir": "^0.4.0", "webpack": "^4.0.0", "webpack-cli": "^4.0.0"}, "scripts": {"docs": "jsdoc -c .jsdoc.js", "pretest": "npm run prepare", "test": "c8 mocha build/test/unit", "lint": "gts check src samples test", "clean": "gts clean", "compile": "tsc -p . && cp src/*.json build/src && cp -r test/fixtures build/test && cp -r protos build/", "compile-operation-protos": "pbjs -t json google/longrunning/operations.proto -p ./protos > protos/operations.json && pbjs -t static-module -r operations_protos google/longrunning/operations.proto -p ./protos > protos/operations.js && pbts protos/operations.js -o protos/operations.d.ts", "compile-compute-operations-protos": "pbjs -t json google/longrunning/compute_operations.proto -p ./protos > protos/compute_operations.json && pbjs -t static-module -r compute_operations_protos google/longrunning/compute_operations.proto -p ./protos > protos/compute_operations.js && pbts protos/compute_operations.js -o protos/compute_operations.d.ts", "compile-iam-protos": "pbjs -t json google/iam/v1/iam_policy.proto google/iam/v1/options.proto google/iam/v1/policy.proto google/iam/v1/logging/audit_data.proto -p ./protos > protos/iam_service.json && pbjs -t static-module -r iam_protos google/iam/v1/iam_policy.proto google/iam/v1/options.proto google/iam/v1/policy.proto google/iam/v1/logging/audit_data.proto -p ./protos > protos/iam_service.js && pbts protos/iam_service.js -o protos/iam_service.d.ts", "compile-location-protos": "pbjs -t json google/cloud/location/locations.proto -p ./protos > protos/locations.json && pbjs -t static-module -r locations_protos google/cloud/location/locations.proto -p ./protos > protos/locations.js && pbts protos/locations.js -o protos/locations.d.ts", "compile-status-protos": "pbjs -t json google/rpc/status.proto google/rpc/error_details.proto -p ./protos > protos/status.json", "compile-http-protos": "pbjs -t static-module -r http_proto --keep-case google/api/http.proto -p ./protos > protos/http.js && pbts protos/http.js -o protos/http.d.ts", "fix": "gts fix", "prepare": "npm run compile && cd ../tools && npm i && npm run compile && cd ../gax && pwd && node ../tools/build/src/prepublish.js . && mkdirp build/protos && cp -r protos/* build/protos/ && npm run minify-proto-json", "system-test": "c8 mocha build/test/system-test --timeout 600000 && npm run test-application", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "browser-test": "cd test/browser-test && npm run prefetch && npm install && npm test", "test-application": "cd test/test-application && npm run prefetch && npm install && npm start", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean", "update-protos": "cd ../tools && npm i && npm run compile && cd ../gax && node ../tools/build/src/listProtos.js .", "minify-proto-json": "cd ../tools && npm i && npm run compile && cd ../gax && node ../tools/build/src/minify.js"}, "keywords": ["grpc"], "repository": {"type": "git", "url": "https://github.com/googleapis/gax-nodejs.git", "directory": "gax"}, "author": "Google API Authors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/googleapis/gax-nodejs/issues"}, "homepage": "https://github.com/googleapis/gax-nodejs#readme", "engines": {"node": ">=14"}, "browser": "build/src/fallback.js"}