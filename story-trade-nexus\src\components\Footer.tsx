
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BookOpen, Mail, Instagram, Twitter, Facebook } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-navy-500 text-white pt-12 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo & About */}
          <div className="col-span-1 md:col-span-1">
            <div className="flex items-center mb-4">
              <BookOpen className="h-6 w-6 text-beige-500 mr-2" />
              <span className="text-xl font-playfair font-bold">PeerBooks</span>
            </div>
            <p className="text-sm text-gray-300 mb-6">
              A peer-to-peer platform for book lovers to rent, buy, and exchange used books directly with each other.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-beige-500">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-300 hover:text-beige-500">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-300 hover:text-beige-500">
                <Instagram className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="col-span-1">
            <h3 className="text-lg font-playfair font-medium mb-4 text-beige-500">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/" className="text-gray-300 hover:text-white transition duration-300">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/browse" className="text-gray-300 hover:text-white transition duration-300">
                  Browse Books
                </Link>
              </li>
              <li>
                <Link to="/how-it-works" className="text-gray-300 hover:text-white transition duration-300">
                  How It Works
                </Link>
              </li>
              <li>
                <Link to="/add-book" className="text-gray-300 hover:text-white transition duration-300">
                  Add a Book
                </Link>
              </li>
            </ul>
          </div>

          {/* Help & Support */}
          <div className="col-span-1">
            <h3 className="text-lg font-playfair font-medium mb-4 text-beige-500">Help & Support</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/faq" className="text-gray-300 hover:text-white transition duration-300">
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-gray-300 hover:text-white transition duration-300">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-gray-300 hover:text-white transition duration-300">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-300 hover:text-white transition duration-300">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/data-deletion" className="text-gray-300 hover:text-white transition duration-300">
                  Data Deletion
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div className="col-span-1">
            <h3 className="text-lg font-playfair font-medium mb-4 text-beige-500">Stay Updated</h3>
            <p className="text-sm text-gray-300 mb-4">
              Subscribe to our newsletter for new books and updates.
            </p>
            <form className="mb-4">
              <div className="flex">
                <input
                  type="email"
                  placeholder="Your email"
                  className="px-3 py-2 text-sm text-gray-900 bg-white border-0 rounded-l-md focus:ring-burgundy-500 focus:border-burgundy-500 flex-grow"
                />
                <button
                  type="submit"
                  className="px-3 py-2 text-sm text-white bg-burgundy-500 rounded-r-md hover:bg-burgundy-600 focus:outline-none"
                >
                  <Mail className="h-4 w-4" />
                </button>
              </div>
            </form>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-6">
          <p className="text-sm text-center text-gray-400">
            &copy; {new Date().getFullYear()} PeerBooks. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
