import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Book<PERSON><PERSON>, User, Menu, BookPlus, LayoutDashboard, AlertTriangle } from 'lucide-react';
import { Button } from './ui/button-variants';
import { useAuth } from '@/lib/AuthContext';
import UserMenu from './UserMenu';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const { currentUser, loading, emailVerified } = useAuth();

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center">
          <Link to="/" className="flex items-center">
            <BookOpen className="h-6 w-6 text-burgundy-500 mr-2" />
            <span className="text-xl font-playfair font-bold text-navy-500">PeerBooks</span>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          <Link to="/" className="text-gray-700 hover:text-burgundy-500 hover-underline-animation">
            Home
          </Link>
          <Link to="/browse" className="text-gray-700 hover:text-burgundy-500 hover-underline-animation">
            Browse Books
          </Link>
          {currentUser && !emailVerified ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link to="/add-books" className="text-gray-700 hover:text-burgundy-500 hover-underline-animation flex items-center">
                    Add Your Books
                    <AlertTriangle className="h-3.5 w-3.5 ml-1 text-amber-500" />
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Email verification required</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : (
            <Link to="/add-books" className="text-gray-700 hover:text-burgundy-500 hover-underline-animation">
              Add Your Books
            </Link>
          )}
          <Link to="/how-it-works" className="text-gray-700 hover:text-burgundy-500 hover-underline-animation">
            How It Works
          </Link>
        </nav>

        <div className="hidden md:flex items-center space-x-4">
          {loading ? (
            // Show loading skeleton when auth state is loading
            <div className="h-9 w-20 bg-gray-200 animate-pulse rounded-md"></div>
          ) : currentUser ? (
            // Show user menu when authenticated
            <UserMenu />
          ) : (
            // Show sign in and join buttons when not authenticated
            <>
              <Link to="/signin">
                <Button variant="link">Sign In</Button>
              </Link>
              <Link to="/join">
                <Button>Join Now</Button>
              </Link>
            </>
          )}
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-gray-700"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <Menu className="h-6 w-6" />
        </button>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-white shadow-lg py-4 px-4 animate-fade-in">
          <nav className="flex flex-col space-y-3">
            <Link
              to="/"
              className="text-gray-700 hover:text-burgundy-500 py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              to="/browse"
              className="text-gray-700 hover:text-burgundy-500 py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Browse Books
            </Link>
            {currentUser && !emailVerified ? (
              <Link
                to="/add-books"
                className="text-gray-700 hover:text-burgundy-500 py-2 flex items-center"
                onClick={() => setIsMenuOpen(false)}
              >
                Add Your Books
                <AlertTriangle className="h-3.5 w-3.5 ml-1 text-amber-500" />
                <span className="ml-1 text-xs text-amber-600">(Verification Required)</span>
              </Link>
            ) : (
              <Link
                to="/add-books"
                className="text-gray-700 hover:text-burgundy-500 py-2"
                onClick={() => setIsMenuOpen(false)}
              >
                Add Your Books
              </Link>
            )}
            <Link
              to="/how-it-works"
              className="text-gray-700 hover:text-burgundy-500 py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              How It Works
            </Link>
            <Link
              to="/contact"
              className="text-gray-700 hover:text-burgundy-500 py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Contact Us
            </Link>
            <div className="pt-2 flex space-x-4">
              {loading ? (
                // Show loading skeleton when auth state is loading
                <div className="h-8 w-full bg-gray-200 animate-pulse rounded-md"></div>
              ) : currentUser ? (
                // Show user-related buttons when authenticated
                <div className="w-full space-y-2">
                  <Link to="/dashboard" onClick={() => setIsMenuOpen(false)} className="block">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <LayoutDashboard className="h-4 w-4 mr-2" />
                      Dashboard
                      {!emailVerified && (
                        <AlertTriangle className="h-3.5 w-3.5 ml-2 text-amber-500" />
                      )}
                    </Button>
                  </Link>
                  <Link to="/profile" onClick={() => setIsMenuOpen(false)} className="block">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <User className="h-4 w-4 mr-2" />
                      My Profile
                      {!emailVerified && (
                        <AlertTriangle className="h-3.5 w-3.5 ml-2 text-amber-500" />
                      )}
                    </Button>
                  </Link>
                  <Link to="/my-books" onClick={() => setIsMenuOpen(false)} className="block">
                    <Button variant="outline" size="sm" className="w-full justify-start">
                      <BookPlus className="h-4 w-4 mr-2" />
                      My Books
                      {!emailVerified && (
                        <AlertTriangle className="h-3.5 w-3.5 ml-2 text-amber-500" />
                      )}
                    </Button>
                  </Link>
                  {!emailVerified && (
                    <Link to="/verify-email" onClick={() => setIsMenuOpen(false)} className="block mt-4">
                      <Button size="sm" className="w-full justify-center bg-amber-500 hover:bg-amber-600">
                        Verify Email
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                // Show sign in and join buttons when not authenticated
                <>
                  <Link to="/signin" onClick={() => setIsMenuOpen(false)}>
                    <Button variant="link" size="sm">Sign In</Button>
                  </Link>
                  <Link to="/join" onClick={() => setIsMenuOpen(false)}>
                    <Button size="sm">Join Now</Button>
                  </Link>
                </>
              )}
            </div>

          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
