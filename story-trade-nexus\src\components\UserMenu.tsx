import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  User,
  LogOut,
  BookOpen,
  Settings,
  ChevronDown,
  Heart,
  MessageSquare,
  LayoutDashboard,
  AlertTriangle,
  Mail
} from "lucide-react";
import { useAuth } from "@/lib/AuthContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button-variants";
import { toast } from "sonner";
import UserProfilePopover from "./UserProfilePopover";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";

/**
 * UserMenu component displays user information and dropdown menu
 * when the user is authenticated
 */
export const UserMenu = () => {
  const { currentUser, userData, signOut, emailVerified, sendVerificationEmail } = useAuth();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [isSendingVerification, setIsSendingVerification] = useState(false);

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (userData?.displayName) {
      return userData.displayName
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2);
    }
    return currentUser?.email?.substring(0, 2).toUpperCase() || "U";
  };

  // Handle sending verification email
  const handleSendVerification = async () => {
    if (!currentUser) return;

    setIsSendingVerification(true);
    try {
      await sendVerificationEmail();
      toast.success("Verification email sent! Please check your inbox.");
    } catch (error) {
      console.error("Error sending verification email:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to send verification email";
      toast.error(errorMessage);
    } finally {
      setIsSendingVerification(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      setIsSigningOut(true);
      await signOut();
      toast.success("Signed out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out");
    } finally {
      setIsSigningOut(false);
    }
  };

  return (
    <div className="flex items-center gap-4">
      {/* User Profile Popover */}
      <UserProfilePopover>
        <Button
          variant="ghost"
          className="flex items-center gap-2 px-2 py-1 hover:bg-burgundy-50 hover:text-burgundy-700 rounded-full cursor-pointer transition-colors"
          aria-label="View Dashboard"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={userData?.photoURL || ""}
              alt={userData?.displayName || "User"}
            />
            <AvatarFallback className="bg-burgundy-100 text-burgundy-700">
              {getInitials()}
            </AvatarFallback>
          </Avatar>
          <span className="hidden md:inline text-sm font-medium">
            {userData?.displayName || currentUser?.email?.split("@")[0] || "User"}
          </span>
        </Button>
      </UserProfilePopover>

      {/* User Dropdown - Hidden but keeping the menu functionality */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="hidden" /* Hide the button completely */
            aria-label="Menu"
          >
            <span className="sr-only">Menu</span>
          </Button>
        </DropdownMenuTrigger>

        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>My Account</DropdownMenuLabel>

          {!emailVerified && (
            <div className="px-2 py-1.5 text-xs bg-amber-50 text-amber-800 border-y border-amber-200 flex items-center gap-1.5">
              <AlertTriangle className="h-3.5 w-3.5" />
              <span>Email not verified</span>
            </div>
          )}

          <DropdownMenuSeparator />

          <DropdownMenuGroup>
            <DropdownMenuItem asChild>
              <Link to="/dashboard" className="cursor-pointer w-full">
                <LayoutDashboard className="mr-2 h-4 w-4" />
                <span>Dashboard</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link to="/profile" className="cursor-pointer w-full">
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link to="/my-books" className="cursor-pointer w-full">
                <BookOpen className="mr-2 h-4 w-4" />
                <span>My Books</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link to="/wishlist" className="cursor-pointer w-full">
                <Heart className="mr-2 h-4 w-4" />
                <span>Wishlist</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild>
              <Link to="/messages" className="cursor-pointer w-full">
                <MessageSquare className="mr-2 h-4 w-4" />
                <span>Messages</span>
              </Link>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link to="/settings" className="cursor-pointer w-full">
              <Settings className="mr-2 h-4 w-4" />
              <span>Settings</span>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          {!emailVerified && (
            <>
              <DropdownMenuItem
                onClick={handleSendVerification}
                disabled={isSendingVerification}
                className="cursor-pointer text-amber-700"
              >
                <Mail className="mr-2 h-4 w-4" />
                <span>{isSendingVerification ? "Sending..." : "Verify Email"}</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
            </>
          )}

          <DropdownMenuItem
            onClick={handleSignOut}
            disabled={isSigningOut}
            className="cursor-pointer"
          >
            <LogOut className="mr-2 h-4 w-4" />
            <span>{isSigningOut ? "Signing out..." : "Sign out"}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default UserMenu;
