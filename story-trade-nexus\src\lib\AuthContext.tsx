import React, { createContext, useContext, useState, useEffect } from 'react';
import { auth, initializeFirebase } from './firebase';
import { createUserDocument, getUserDocument, UserData, isUserAdmin } from './userService';
import { UserRole } from '@/types';

// Define User type to avoid importing from firebase/auth
type User = {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
};

interface AdditionalUserData {
  phone?: string;
  address?: string;
  apartment?: string;
  city?: string;
  state?: string;
  pincode?: string;
  gpsCoordinates?: any;
  [key: string]: any;
}

interface AuthContextType {
  currentUser: User | null;
  userData: UserData | null;
  loading: boolean;
  emailVerified: boolean;
  isAdmin: boolean;
  signUp: (
    email: string,
    password: string,
    displayName: string,
    additionalData?: AdditionalUserData
  ) => Promise<User>;
  signIn: (email: string, password: string) => Promise<User>;
  signInWithGoogle: () => Promise<User>;
  signInWithFacebook: () => Promise<User>;
  signOut: () => Promise<void>;
  sendVerificationEmail: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  reloadUser: () => Promise<boolean>;
  checkAdminStatus: () => Promise<boolean>;
  refreshUserData: () => Promise<UserData | null>;
  isNewUser: boolean;
  setIsNewUser: (value: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [emailVerified, setEmailVerified] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [isNewUser, setIsNewUser] = useState(false);

  // Function to check if the current user is an admin
  const checkAdminStatus = async (): Promise<boolean> => {
    if (!currentUser) {
      console.log("checkAdminStatus: No current user");
      setIsAdmin(false);
      return false;
    }

    try {
      console.log("checkAdminStatus: Checking admin status for user:", currentUser.uid);
      console.log("checkAdminStatus: User email:", currentUser.email);

      // Special <NAME_EMAIL> - always make them admin
      if (currentUser.email === '<EMAIL>') {
        console.log("checkAdminStatus: Special admin user detected");

        try {
          // Dynamically import Firestore functions
          const { doc, setDoc, getDoc } = await import('firebase/firestore');

          // Get the user document reference
          const userRef = doc(db, 'users', currentUser.uid);

          // Check if the document exists
          const docSnap = await getDoc(userRef);

          if (docSnap.exists()) {
            const userData = docSnap.data();
            console.log("checkAdminStatus: Existing user document:", userData);

            // If user doesn't have admin role, update it
            if (userData.role !== UserRole.Admin) {
              console.log("checkAdminStatus: Updating special user to admin role");
              await setDoc(userRef, { role: UserRole.Admin }, { merge: true });
              console.log("checkAdminStatus: Special user updated to admin role");
            }
          } else {
            // Create a new user document with admin role
            console.log("checkAdminStatus: Creating new user document with admin role");
            await setDoc(userRef, {
              uid: currentUser.uid,
              email: currentUser.email,
              displayName: currentUser.displayName || 'Admin User',
              role: UserRole.Admin,
              createdAt: new Date().toISOString()
            });
            console.log("checkAdminStatus: New admin user document created");
          }

          // Refresh user data after update
          const updatedUserDoc = await getUserDocument(currentUser.uid);
          console.log("checkAdminStatus: Updated user document:", updatedUserDoc);
          setUserData(updatedUserDoc);

        } catch (updateError) {
          console.error("checkAdminStatus: Error updating special user role:", updateError);
          // Even if the update fails, still grant admin access to this special user
        }

        // Always set admin status to true for this special user
        setIsAdmin(true);
        return true;
      }

      // For other users, first try to get the user document directly
      const userDoc = await getUserDocument(currentUser.uid);
      console.log("checkAdminStatus: User document:", userDoc);

      // Check their role in the user document
      if (userDoc) {
        const adminStatus = userDoc.role === UserRole.Admin;
        console.log("checkAdminStatus: User admin status from document:", adminStatus);
        setIsAdmin(adminStatus);
        return adminStatus;
      }

      // If no user document found, fall back to isUserAdmin function
      console.log("checkAdminStatus: No user document found, falling back to isUserAdmin");
      const adminStatus = await isUserAdmin(currentUser.uid);
      console.log("checkAdminStatus: Admin status from isUserAdmin:", adminStatus);
      setIsAdmin(adminStatus);
      return adminStatus;
    } catch (error) {
      console.error("checkAdminStatus: Error checking admin status:", error);
      setIsAdmin(false);
      return false;
    }
  };

  useEffect(() => {
    let unsubscribe: () => void = () => {};

    const setupAuthObserver = async () => {
      try {
        // Initialize Firebase
        await initializeFirebase();

        // Dynamically import Firebase auth functions
        const { onAuthStateChanged } = await import('firebase/auth');

        console.log("Setting up auth state observer...");
        unsubscribe = onAuthStateChanged(auth, async (user) => {
          console.log("Auth state changed:", user ? `User: ${user.uid}` : "No user");
          setCurrentUser(user);

          if (user) {
            // Reload the user to get the latest verification status
            try {
              const { reload: firebaseReloadUser } = await import('firebase/auth');
              await firebaseReloadUser(user);
              console.log("User reloaded in auth state change");
            } catch (reloadError) {
              console.error("Error reloading user in auth state change:", reloadError);
              // Continue with the current user state
            }

            // Set email verification status after reload
            setEmailVerified(user.emailVerified);
            console.log("Email verification status after reload:", user.emailVerified);

            try {
              // Get user data from Firestore
              console.log("Fetching user data from Firestore...");
              const userDoc = await getUserDocument(user.uid);
              setUserData(userDoc);
              console.log("User data:", userDoc);

              // If user document doesn't exist, create it
              if (!userDoc) {
                try {
                  console.log("Creating user document for existing user");
                  await createUserDocument(user);

                  // Fetch the newly created document
                  const newUserDoc = await getUserDocument(user.uid);
                  setUserData(newUserDoc);
                  console.log("New user document created:", newUserDoc);
                } catch (createError) {
                  console.error("Error creating user document for existing user:", createError);
                }
              }

              // Special <NAME_EMAIL> - always check admin status
              if (user.email === '<EMAIL>') {
                console.log("Special admin user detected in auth state change");
                // Use checkAdminStatus to ensure admin role is set
                await checkAdminStatus();
              } else {
                // For other users, check if user is an admin from the document
                const adminStatus = userDoc?.role === UserRole.Admin;
                setIsAdmin(adminStatus);
                console.log("User admin status:", adminStatus);
              }
            } catch (error) {
              console.error("Error fetching user data:", error);
              // Continue without throwing - user is still authenticated
            }
          } else {
            setUserData(null);
            setEmailVerified(false);
            setIsAdmin(false);
            console.log("User signed out or no user");
          }

          setLoading(false);
        });
      } catch (error) {
        console.error("Error setting up auth observer:", error);
        setLoading(false);
      }
    };

    setupAuthObserver();

    return () => unsubscribe();
  }, []);

  // Sign up with email and password
  const signUp = async (
    email: string,
    password: string,
    displayName: string,
    additionalData?: AdditionalUserData
  ) => {
    console.log("AuthContext signUp called with:", { email, displayName });
    console.log("Additional data:", additionalData);

    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const {
        createUserWithEmailAndPassword,
        updateProfile,
        sendEmailVerification
      } = await import('firebase/auth');

      // First create the user with Firebase Authentication
      console.log("Creating user with Firebase Authentication...");
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      console.log("User created in Firebase Auth:", userCredential.user.uid);

      try {
        // Update the user's profile with the display name
        console.log("Updating user profile with display name:", displayName);
        await updateProfile(userCredential.user, { displayName });
        console.log("User profile updated successfully");

        // Send email verification
        try {
          console.log("Sending verification email...");
          await sendEmailVerification(userCredential.user);
          console.log("Verification email sent successfully");
        } catch (verificationError) {
          console.error("Error sending verification email:", verificationError);
          // Continue without throwing - user is still created
        }

        // Create user document in Firestore
        // If Firestore operations fail, we still have the authenticated user
        try {
          console.log("Creating user document in Firestore...");
          await createUserDocument(userCredential.user, additionalData);
          console.log("User document created successfully in Firestore");
        } catch (firestoreError) {
          console.error("Error creating user document in Firestore:", firestoreError);
          // Continue without throwing - user is still authenticated
        }
      } catch (profileError) {
        console.error("Error updating user profile:", profileError);
        // Continue without throwing - user is still authenticated
      }

      console.log("User registration completed successfully");
      return userCredential.user;
    } catch (error) {
      console.error("Error signing up:", error);
      throw error;
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const {
        signInWithEmailAndPassword,
        reload: firebaseReloadUser
      } = await import('firebase/auth');

      // Authenticate the user
      console.log("Signing in user with email:", email);
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      console.log("User signed in successfully:", userCredential.user.uid);

      // Reload the user to get the latest verification status
      try {
        await firebaseReloadUser(userCredential.user);
      } catch (reloadError) {
        console.error("Error reloading user after sign in:", reloadError);
        // Continue with the current user state
      }

      // Check if email is verified
      if (!userCredential.user.emailVerified) {
        setEmailVerified(false);
        console.log("User email is not verified");
      } else {
        setEmailVerified(true);
        console.log("User email is verified");
      }

      try {
        // Get user data from Firestore
        console.log("Fetching user data from Firestore...");
        const userDoc = await getUserDocument(userCredential.user.uid);
        setUserData(userDoc);
        console.log("User data fetched successfully:", userDoc);
      } catch (firestoreError) {
        console.error("Error fetching user data from Firestore:", firestoreError);
        // Continue without throwing - user is still authenticated
      }

      return userCredential.user;
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { signOut: firebaseSignOut } = await import('firebase/auth');

      await firebaseSignOut(auth);
      setUserData(null);
      setEmailVerified(false);
      console.log("User signed out successfully");
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  };

  // Send verification email
  const sendVerificationEmail = async () => {
    if (!currentUser) {
      throw new Error("No user is currently signed in");
    }

    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { sendEmailVerification } = await import('firebase/auth');

      await sendEmailVerification(currentUser);
      console.log("Verification email sent successfully");
    } catch (error) {
      console.error("Error sending verification email:", error);
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { sendPasswordResetEmail } = await import('firebase/auth');

      await sendPasswordResetEmail(auth, email);
      console.log("Password reset email sent successfully");
    } catch (error) {
      console.error("Error sending password reset email:", error);
      throw error;
    }
  };

  // Reload user to check for updated email verification status
  const reloadUser = async (): Promise<boolean> => {
    if (!currentUser) {
      console.error("No user is currently signed in");
      return false;
    }

    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const { reload: firebaseReloadUser } = await import('firebase/auth');

      // Reload the user object from Firebase
      await firebaseReloadUser(currentUser);
      console.log("User reloaded successfully");

      // Get the current user again from auth to ensure we have the latest data
      const user = auth.currentUser;

      if (!user) {
        console.error("User is null after reload");
        setEmailVerified(false);
        return false;
      }

      // For social login users, we need to be extra careful about verification status
      const provider = user.providerData[0]?.providerId;
      const isSocialLogin = provider === 'google.com' || provider === 'facebook.com';

      // Update the email verification status
      const isVerified = user.emailVerified;
      console.log(`User email verification status: ${isVerified}, Provider: ${provider}`);

      // Set the emailVerified state
      setEmailVerified(isVerified);

      // If this is a social login user and they're not verified, log it clearly
      if (isSocialLogin && !isVerified) {
        console.log(`IMPORTANT: Social login user (${provider}) is NOT verified. Enforcing verification requirement.`);
      }

      return isVerified;
    } catch (error) {
      console.error("Error reloading user:", error);
      // In case of error, default to not verified for safety
      setEmailVerified(false);
      return false;
    }
  };

  // Refresh user data from Firestore
  const refreshUserData = async (): Promise<UserData | null> => {
    if (!currentUser) {
      console.error("No user is currently signed in");
      return null;
    }

    try {
      // Get user data from Firestore
      const userDoc = await getUserDocument(currentUser.uid);
      setUserData(userDoc);
      return userDoc;
    } catch (error) {
      console.error("Error refreshing user data:", error);
      return null;
    }
  };

  // Sign in with Google
  const signInWithGoogle = async (): Promise<User> => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const {
        GoogleAuthProvider,
        signInWithPopup,
        sendEmailVerification
      } = await import('firebase/auth');

      // Create a Google auth provider
      const provider = new GoogleAuthProvider();

      // Add scopes for additional user information
      provider.addScope('profile');
      provider.addScope('email');

      console.log("Signing in with Google...");
      const result = await signInWithPopup(auth, provider);
      console.log("Google sign-in successful:", result.user.uid);

      // Check if this is a new user
      const isNewUserSignUp = result.user.metadata.creationTime === result.user.metadata.lastSignInTime;
      setIsNewUser(isNewUserSignUp);
      console.log("Is new user:", isNewUserSignUp);

      // For all Google sign-ins, we need to ensure email verification is enforced
      // Even if Google marks the email as verified, we want to enforce our own verification
      if (isNewUserSignUp) {
        try {
          console.log("Sending verification email to new Google user...");

          // Force email verification for all new Google users
          // This ensures our verification policy is enforced regardless of Google's verification status

          // First, set emailVerified to false to ensure access restrictions are applied
          setEmailVerified(false);

          // Then send the verification email
          await sendEmailVerification(result.user);
          console.log("Verification email sent successfully to new Google user");

        } catch (verificationError) {
          console.error("Error sending verification email to new Google user:", verificationError);
          // Continue without throwing - user is still authenticated

          // Ensure emailVerified is set to false
          setEmailVerified(false);
        }
      } else if (!isNewUserSignUp) {
        // For existing users, reload to get the latest verification status
        try {
          const { reload: firebaseReloadUser } = await import('firebase/auth');
          await firebaseReloadUser(result.user);
          console.log("Existing Google user reloaded");

          // Update emailVerified state based on the user's current status
          setEmailVerified(result.user.emailVerified);
          console.log("Existing Google user email verification status:", result.user.emailVerified);
        } catch (reloadError) {
          console.error("Error reloading existing Google user:", reloadError);
          // Default to the current verification status
          setEmailVerified(result.user.emailVerified);
        }

        // For existing users, we'll check if they have a user document
        try {
          // Get user data from Firestore
          console.log("Fetching user data from Firestore...");
          const userDoc = await getUserDocument(result.user.uid);
          setUserData(userDoc);
          console.log("User data fetched successfully:", userDoc);
        } catch (firestoreError) {
          console.error("Error fetching user data from Firestore:", firestoreError);
          // Continue without throwing - user is still authenticated
        }
      }

      return result.user;
    } catch (error) {
      console.error("Error signing in with Google:", error);
      throw error;
    }
  };

  // Sign in with Facebook
  const signInWithFacebook = async (): Promise<User> => {
    try {
      // Initialize Firebase if not already initialized
      await initializeFirebase();

      // Dynamically import Firebase auth functions
      const {
        FacebookAuthProvider,
        signInWithPopup,
        sendEmailVerification
      } = await import('firebase/auth');

      // Create a Facebook auth provider
      const provider = new FacebookAuthProvider();

      // Add scopes for additional user information
      provider.addScope('email');
      provider.addScope('public_profile');

      console.log("Signing in with Facebook...");
      const result = await signInWithPopup(auth, provider);
      console.log("Facebook sign-in successful:", result.user.uid);

      // Check if this is a new user
      const isNewUserSignUp = result.user.metadata.creationTime === result.user.metadata.lastSignInTime;
      setIsNewUser(isNewUserSignUp);
      console.log("Is new user:", isNewUserSignUp);

      // For all Facebook sign-ins, we need to ensure email verification is enforced
      // Even if Facebook marks the email as verified, we want to enforce our own verification
      if (isNewUserSignUp) {
        try {
          console.log("Sending verification email to new Facebook user...");

          // Force email verification for all new Facebook users
          // This ensures our verification policy is enforced regardless of Facebook's verification status

          // First, set emailVerified to false to ensure access restrictions are applied
          setEmailVerified(false);

          // Then send the verification email
          await sendEmailVerification(result.user);
          console.log("Verification email sent successfully to new Facebook user");

        } catch (verificationError) {
          console.error("Error sending verification email to new Facebook user:", verificationError);
          // Continue without throwing - user is still authenticated

          // Ensure emailVerified is set to false
          setEmailVerified(false);
        }
      } else if (!isNewUserSignUp) {
        // For existing users, reload to get the latest verification status
        try {
          const { reload: firebaseReloadUser } = await import('firebase/auth');
          await firebaseReloadUser(result.user);
          console.log("Existing Facebook user reloaded");

          // Update emailVerified state based on the user's current status
          setEmailVerified(result.user.emailVerified);
          console.log("Existing Facebook user email verification status:", result.user.emailVerified);
        } catch (reloadError) {
          console.error("Error reloading existing Facebook user:", reloadError);
          // Default to the current verification status
          setEmailVerified(result.user.emailVerified);
        }

        // For existing users, we'll check if they have a user document
        try {
          // Get user data from Firestore
          console.log("Fetching user data from Firestore...");
          const userDoc = await getUserDocument(result.user.uid);
          setUserData(userDoc);
          console.log("User data fetched successfully:", userDoc);
        } catch (firestoreError) {
          console.error("Error fetching user data from Firestore:", firestoreError);
          // Continue without throwing - user is still authenticated
        }
      }

      return result.user;
    } catch (error) {
      console.error("Error signing in with Facebook:", error);
      throw error;
    }
  };

  const value = {
    currentUser,
    userData,
    loading,
    emailVerified,
    isAdmin,
    signUp,
    signIn,
    signInWithGoogle,
    signInWithFacebook,
    signOut,
    sendVerificationEmail,
    resetPassword,
    reloadUser,
    checkAdminStatus,
    refreshUserData,
    isNewUser,
    setIsNewUser
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
